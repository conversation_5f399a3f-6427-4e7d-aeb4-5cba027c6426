<template>
  <div ref="mapContainer" :style="mapStyle"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch, nextTick, computed } from 'vue'
import { loadBMap } from '/@/utils/map'

interface MapProps {
  center?: { lng: number; lat: number }
  zoom?: number
  scrollWheelZoom?: boolean
  mapStyle?: any
  markers?: any[]
}

interface Emits {
  (e: 'ready', payload: { BMap: any; map: any }): void
  (e: 'markerClick', payload: any): void
}

const props = withDefaults(defineProps<MapProps>(), {
  center: () => ({ lng: 116.40605, lat: 39.915879 }),
  zoom: 10,
  scrollWheelZoom: true,
  markers: () => []
})

const emit = defineEmits<Emits>()

// Refs
const mapContainer = ref<HTMLElement>()
let map: any = null
let BMap: any = null
const markersOnMap: any[] = []

// 计算属性
const mapStyle = computed(() => ({
  width: '100%',
  height: '100%',
  ...props.mapStyle
}))

// 初始化地图
const initMap = async () => {
  try {
    BMap = await loadBMap()
    
    if (!mapContainer.value) return
    
    // 创建地图实例
    map = new BMap.Map(mapContainer.value)
    
    // 设置中心点和缩放级别
    const point = new BMap.Point(props.center.lng, props.center.lat)
    map.centerAndZoom(point, props.zoom)
    
    // 启用滚轮缩放
    if (props.scrollWheelZoom) {
      map.enableScrollWheelZoom(true)
    }
    
    // 添加控件
    map.addControl(new BMap.MapTypeControl())
    map.addControl(new BMap.NavigationControl())
    map.addControl(new BMap.ScaleControl())
    
    // 触发 ready 事件
    emit('ready', { BMap, map })
    
    // 添加标记点
    addMarkers()
    
  } catch (error) {
    console.error('百度地图初始化失败:', error)
  }
}

// 添加标记点
const addMarkers = () => {
  if (!map || !BMap) return
  
  // 清除现有标记
  clearMarkers()
  
  props.markers.forEach((markerData) => {
    const point = new BMap.Point(markerData.longitude, markerData.latitude)
    const marker = new BMap.Marker(point)
    
    // 设置图标
    if (markerData.icon) {
      const icon = new BMap.Icon(markerData.icon.url, new BMap.Size(markerData.icon.size.width, markerData.icon.size.height))
      marker.setIcon(icon)
    }
    
    // 添加点击事件
    marker.addEventListener('click', () => {
      emit('markerClick', markerData)
    })
    
    // 添加信息窗口
    if (markerData.infoWindow) {
      const infoWindow = new BMap.InfoWindow(markerData.infoWindow.content, {
        width: markerData.infoWindow.width || 250,
        height: markerData.infoWindow.height || 100
      })
      
      marker.addEventListener('click', () => {
        map.openInfoWindow(infoWindow, point)
      })
    }
    
    map.addOverlay(marker)
    markersOnMap.push(marker)
  })
}

// 清除标记点
const clearMarkers = () => {
  markersOnMap.forEach(marker => {
    map.removeOverlay(marker)
  })
  markersOnMap.length = 0
}

// 设置地图样式
const setMapStyle = (style: string) => {
  if (map && style !== 'normal') {
    map.setMapStyle({ style })
  }
}

// 监听属性变化
watch(() => props.center, (newCenter) => {
  if (map && BMap) {
    const point = new BMap.Point(newCenter.lng, newCenter.lat)
    map.setCenter(point)
  }
})

watch(() => props.zoom, (newZoom) => {
  if (map) {
    map.setZoom(newZoom)
  }
})

watch(() => props.markers, () => {
  addMarkers()
}, { deep: true })

// 生命周期
onMounted(async () => {
  await nextTick()
  initMap()
})

onBeforeUnmount(() => {
  if (map) {
    map.clearOverlays()
    map = null
  }
})

// 暴露方法给父组件
defineExpose({
  map,
  BMap,
  setMapStyle,
  addMarkers,
  clearMarkers
})
</script>

<style lang="scss" scoped>
// 百度地图容器样式
:deep(.BMap_mask) {
  display: none !important;
}

:deep(.anchorBL) {
  display: none !important;
}
</style>
