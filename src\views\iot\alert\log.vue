<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="68px">
                    <el-form-item label="告警名称" prop="alertName">
                        <el-input v-model="state.tableData.param.alertName" clearable size="default"
                            placeholder="请输入告警名称" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="告警级别" prop="alertLevel">
                        <el-select v-model="state.tableData.param.alertLevel" placeholder="请选择告警级别" clearable
                            size="default" style="width: 240px">
                            <el-option v-for="dict in alert_level_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="处理状态" prop="status">
                        <el-select v-model="state.tableData.param.status" placeholder="请选择处理状态" clearable size="default"
                            style="width: 180px">
                            <el-option v-for="dict in process_status_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="dict.dictValue" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column label="告警名称" align="center" prop="alertName" />
                <el-table-column label="设备编号" align="center" prop="serialNumber" />
                <el-table-column label="设备名称" align="center" prop="deviceName" />
                <el-table-column label="告警级别" align="center" prop="alertLevel" width="120">
                    <template #default="scope">
                        <dict-tag :options="alert_level_list" :value="scope.row.alertLevel" />
                    </template>
                </el-table-column>
                <el-table-column label="告警时间" align="center" prop="createTime" width="170">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.alertTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="数据" align="left" header-align="center" prop="detail">
                    <template #default="scope">
                        <div v-html="formatDetail(scope.row.detail)"></div>
                    </template>
                </el-table-column>
                <el-table-column label="处理状态" align="center" prop="status">
                    <template #default="scope">
                        <dict-tag :options="process_status_list" :value="scope.row.status" />
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="100">
                    <template #default="scope">
                        <el-button size="default" text type="primary" @click="handleUpdate(scope.row)"
                            v-auths="['iot:alertLog:edit']"><el-icon>
                                <Edit />
                            </el-icon>处理</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>
        <!-- 添加或修改设备告警对话框 -->
        <el-dialog style="position: absolute; top: 100px;" :title="dialogData.tableData.dialog.title"
            v-model="dialogData.tableData.dialog.isShowDialog" width="1000px" append-to-body>
            <el-form ref="DialogFormRef" :model="dialogData.tableData.ruleForm" :rules="isReadOnlyMode ? {} : rules" label-width="80px">
                <el-form-item label="处理状态" prop="status">
                    <el-select v-model="dialogData.tableData.ruleForm.status" placeholder="请选择处理状态"
                        style="width: 100%" size="default" :disabled="isReadOnlyMode">
                        <el-option v-for="dict in process_status_list" :key="dict.dictValue"
                            :label="dict.dictLabel" :value="parseInt(dict.dictValue)"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="处理结果" prop="remark">
                    <el-input v-model="dialogData.tableData.ruleForm.remark" type="textarea"
                        :placeholder="isReadOnlyMode ? '' : '请输入处理结果详情'"
                        :rows="3" :disabled="isReadOnlyMode" />
                </el-form-item>
            </el-form>

            <!-- 工单列表 -->
            <div class="ticket-section" style="margin-top: 20px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h4 style="margin: 0; color: #606266;">相关工单</h4>
                    <el-button type="primary" size="small" @click="openCreateTicketDialog" :disabled="isReadOnlyMode">
                        <el-icon><ele-Plus /></el-icon>
                        创建工单
                    </el-button>
                </div>

                <el-table :data="ticketTableData.data" v-loading="ticketTableData.loading"
                    border style="width: 100%" size="small"
                    :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                    <el-table-column label="工单标题" align="center" prop="title" min-width="120" />
                    <el-table-column label="工单类型" align="center" prop="ticketType" width="100">
                        <template #default="scope">
                            <dict-tag :options="work_order_type_list" :value="scope.row.ticketType" />
                        </template>
                    </el-table-column>
                    <el-table-column label="工单级别" align="center" prop="ticketLevel" width="100">
                        <template #default="scope">
                            <dict-tag :options="ticket_level_list" :value="scope.row.ticketLevel" />
                        </template>
                    </el-table-column>
                    <el-table-column label="指派人员" align="center" prop="assignedTo" width="100">
                        <template #default="scope">
                            {{ getUserName(scope.row.assignedTo) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="状态" align="center" prop="status" width="80">
                        <template #default="scope">
                            <dict-tag :options="ticket_status_list" :value="scope.row.status" />
                        </template>
                    </el-table-column>
                    <el-table-column label="截止日期" align="center" prop="dueDate" width="150">
                        <template #default="scope">
                            <span>{{ parseTime(scope.row.dueDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                        </template>
                    </el-table-column>
<!--                    <el-table-column label="创建时间" align="center" prop="createTime" width="150">-->
<!--                        <template #default="scope">-->
<!--                            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>-->
<!--                        </template>-->
<!--                    </el-table-column>-->
                    <el-table-column label="操作" align="center" width="120">
                        <template #default="scope">
                            <el-button size="small" text type="primary" @click="handleTicketDetail(scope.row)">
                                详情
                            </el-button>
                            <el-button size="small" text type="danger" @click="handleDeleteTicket(scope.row)">
                                删除
                            </el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 工单列表分页 -->
                <el-pagination
                    v-if="ticketTableData.total > 0"
                    @size-change="onTicketSizeChange"
                    @current-change="onTicketCurrentChange"
                    class="mt15"
                    style="justify-content: flex-end;"
                    :pager-count="5"
                    :page-sizes="[5, 10, 20]"
                    v-model:current-page="ticketTableData.param.pageNum"
                    background
                    v-model:page-size="ticketTableData.param.pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="ticketTableData.total">
                </el-pagination>

                <!-- 空状态提示 -->
<!--                <div v-if="!ticketTableData.loading && ticketTableData.data.length === 0"-->
<!--                    style="text-align: center; padding: 40px 0; color: #909399;">-->
<!--                    暂无相关工单-->
<!--                </div>-->
            </div>
            <template #footer>
                <el-button @click="cancel">{{ isReadOnlyMode ? '关 闭' : '取 消' }}</el-button>
                <el-button v-if="!isReadOnlyMode" type="primary" @click="SubmitForm(DialogFormRef)">确 定</el-button>
            </template>
        </el-dialog>

        <!-- 创建工单对话框 -->
        <el-dialog title="创建工单" v-model="createTicketDialog.isShow" width="600px" append-to-body>
            <el-form ref="CreateTicketFormRef" :model="createTicketDialog.ruleForm" :rules="ticketRules" label-width="80px">
                <el-form-item label="工单标题" prop="title">
                    <el-input v-model="createTicketDialog.ruleForm.title" placeholder="请输入工单标题" />
                </el-form-item>
                <el-form-item label="工单描述">
                    <el-input v-model="createTicketDialog.ruleForm.description" type="textarea"
                        placeholder="请输入工单描述（可选）" :rows="4" />
                </el-form-item>
                <el-form-item label="工单类型" prop="ticketType">
                    <el-select v-model="createTicketDialog.ruleForm.ticketType" placeholder="请选择工单类型" style="width: 100%">
                        <el-option v-for="dict in work_order_type_list" :key="dict.dictValue"
                            :label="dict.dictLabel" :value="dict.dictValue"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="工单级别" prop="ticketLevel">
                    <el-select v-model="createTicketDialog.ruleForm.ticketLevel" placeholder="请选择工单级别" style="width: 100%">
                        <el-option v-for="dict in ticket_level_list" :key="dict.dictValue"
                            :label="dict.dictLabel" :value="dict.dictValue"></el-option>
                    </el-select>
                </el-form-item>
              <el-form-item label="工单状态" prop="status">
                <el-select v-model="createTicketDialog.ruleForm.status" placeholder="请选择工单状态" style="width: 100%">
                  <el-option v-for="dict in ticket_status_list" :key="dict.dictValue"
                             :label="dict.dictLabel" :value="dict.dictValue"></el-option>
                </el-select>
              </el-form-item>
                <el-form-item label="指派人员" prop="assignedTo">
                    <el-select v-model="createTicketDialog.ruleForm.assignedTo" placeholder="请选择指派人员" style="width: 100%">
                        <el-option v-for="user in userList" :key="user.userName"
                            :label="user.nickName" :value="user.userName"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="截止日期" prop="dueDate">
                    <el-date-picker v-model="createTicketDialog.ruleForm.dueDate" type="datetime"
                        placeholder="请选择截止日期" style="width: 100%"
                        format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                        :disabled-date="disabledDate" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="createTicketDialog.isShow = false">取 消</el-button>
                <el-button type="primary" @click="submitCreateTicket" :loading="createTicketDialog.loading">确 定</el-button>
            </template>
        </el-dialog>

        <!-- 工单详情对话框 -->
        <el-dialog :title="ticketDetailDialog.title" v-model="ticketDetailDialog.isShow" width="600px" append-to-body>
            <!-- 查看模式 -->
            <div v-if="!ticketDetailDialog.isEdit" class="ticket-detail">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="工单标题">{{ ticketDetailDialog.ruleForm.title }}</el-descriptions-item>
                    <el-descriptions-item label="工单类型">
                        <dict-tag :options="work_order_type_list" :value="ticketDetailDialog.ruleForm.ticketType" />
                    </el-descriptions-item>
                    <el-descriptions-item label="工单级别">
                        <dict-tag :options="ticket_level_list" :value="ticketDetailDialog.ruleForm.ticketLevel" />
                    </el-descriptions-item>
                    <el-descriptions-item label="指派人员">{{ getUserName(ticketDetailDialog.ruleForm.assignedTo) }}</el-descriptions-item>
                    <el-descriptions-item label="状态">
                        <dict-tag :options="ticket_status_list" :value="ticketDetailDialog.ruleForm.status" />
                    </el-descriptions-item>
                    <el-descriptions-item label="截止日期">{{ parseTime(ticketDetailDialog.ruleForm.dueDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
                    <el-descriptions-item label="创建人">{{ ticketDetailDialog.ruleForm.createBy }}</el-descriptions-item>
                    <el-descriptions-item label="创建时间">{{ parseTime(ticketDetailDialog.ruleForm.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
                    <el-descriptions-item label="工单描述" :span="2">
                        <div style="white-space: pre-wrap;">{{ ticketDetailDialog.ruleForm.description }}</div>
                    </el-descriptions-item>
                </el-descriptions>
            </div>

            <!-- 编辑模式 -->
            <el-form v-else ref="TicketDetailFormRef" :model="ticketDetailDialog.ruleForm" :rules="ticketRules" label-width="80px">
                <el-form-item label="工单标题" prop="title">
                    <el-input v-model="ticketDetailDialog.ruleForm.title" placeholder="请输入工单标题" />
                </el-form-item>
                <el-form-item label="工单描述">
                    <el-input v-model="ticketDetailDialog.ruleForm.description" type="textarea"
                        placeholder="请输入工单描述（可选）" :rows="4" />
                </el-form-item>
                <el-form-item label="工单类型" prop="ticketType">
                    <el-select v-model="ticketDetailDialog.ruleForm.ticketType" placeholder="请选择工单类型" style="width: 100%">
                        <el-option v-for="dict in work_order_type_list" :key="dict.dictValue"
                            :label="dict.dictLabel" :value="dict.dictValue"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="工单状态" prop="status">
                    <el-select v-model="ticketDetailDialog.ruleForm.status" placeholder="请选择工单状态" style="width: 100%">
                        <el-option v-for="dict in ticket_status_list" :key="dict.dictValue"
                            :label="dict.dictLabel" :value="dict.dictValue"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="工单级别" prop="ticketLevel">
                    <el-select v-model="ticketDetailDialog.ruleForm.ticketLevel" placeholder="请选择工单级别" style="width: 100%">
                        <el-option v-for="dict in ticket_level_list" :key="dict.dictValue"
                            :label="dict.dictLabel" :value="dict.dictValue"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="指派人员" prop="assignedTo">
                    <el-select v-model="ticketDetailDialog.ruleForm.assignedTo" placeholder="请选择指派人员" style="width: 100%">
                        <el-option v-for="user in userList" :key="user.userName"
                            :label="user.nickName" :value="user.userName"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="截止日期" prop="dueDate">
                    <el-date-picker v-model="ticketDetailDialog.ruleForm.dueDate" type="datetime"
                        placeholder="请选择截止日期" style="width: 100%"
                        format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"
                        :disabled-date="disabledDate" />
                </el-form-item>
            </el-form>

            <template #footer>
                <div v-if="!ticketDetailDialog.isEdit">
                    <el-button @click="ticketDetailDialog.isShow = false">关 闭</el-button>
                    <el-button type="primary" @click="switchToEditMode">编 辑</el-button>
                </div>
                <div v-else>
                    <el-button @click="cancelEdit">取 消</el-button>
                    <el-button type="primary" @click="submitTicketUpdate" :loading="ticketDetailDialog.loading">保 存</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="">
import { reactive, onMounted, ref, computed } from 'vue';
import { ElMessage, ElMessageBox, FormInstance } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
import { parseTime } from '/@/utils/next'
import { addAlertLog, getAlertLog, listAlertLog, updateAlertLog } from '/@/api/iot/alertLog';
import { listTicket, addTicket, getTicket, updateTicket, delTicket } from '/@/api/iot/ticket';
import { listUser} from "/@/api/system/user";


const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
interface Option {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}
// 定义变量内容
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            status: '',
            alertName: '',
            alertLevel: '',

        },
    },
});
const dialogData = reactive({
    tableData: {
        ruleForm: {
            remark: '',
            serialNumber: '',
            status: '' as any,
            alertLogId: '' as any,
            createWorkOrder: false,
            workOrderType: '',
            assignedUser: '',
            deadline: '',
            selWorkOrder: false,
            workOrderDetail: ''
        },
        dialog: {
            isShowDialog: false,
            title: '',
        },
    },
})
// 校验规则
const rules = reactive({
    status: [
        {
            required: true,
            message: '请选择处理状态',
            trigger: 'change',
        },
    ],
    remark: [
        {
            required: true,
            message: '处理结果不能为空',
            trigger: 'blur',
        },
    ],
    workOrderType: [
        {
            required: true,
            message: '请选择工单类型',
            trigger: 'change',
        },
    ],
    assignedUser: [
        {
            required: true,
            message: '请选择指派人员',
            trigger: 'change',
        },
    ],
    deadline: [
        {
            required: true,
            message: '请选择截止日期',
            trigger: 'change',
        },
    ],
})

// 工单表单验证规则
const ticketRules = reactive({
    title: [
        { required: true, message: '请输入工单标题', trigger: 'blur' },
    ],
    ticketType: [
        { required: true, message: '请选择工单类型', trigger: 'change' },
    ],
    status: [
        { required: true, message: '请选择工单状态', trigger: 'change' },
    ],
    ticketLevel: [
        { required: true, message: '请选择工单级别', trigger: 'change' },
    ],
    assignedTo: [
        { required: true, message: '请选择指派人员', trigger: 'change' },
    ],
    dueDate: [
        { required: true, message: '请选择截止日期', trigger: 'change' },
    ],
});

// 定义变量内容
const alert_level_list = ref<Option[]>([]);
const process_status_list = ref<Option[]>([]);
const work_order_type_list = ref<Option[]>([]);
const ticket_status_list = ref<Option[]>([]);
const ticket_level_list = ref<Option[]>([]);
const userList = ref<any[]>([]);
const DialogFormRef = ref();
const CreateTicketFormRef = ref();
const TicketDetailFormRef = ref();

// 当前处理的告警行数据
const currentAlertRow = ref<any>(null);

// 记录初始加载时的处理状态
const initialStatus = ref<number | null>(null);

// 工单表格数据
const ticketTableData = reactive({
    data: [],
    total: 0,
    loading: false,
    param: {
        pageNum: 1,
        pageSize: 5,
        alertLogId: '' as any,
    },
});

// 创建工单对话框数据
const createTicketDialog = reactive({
    isShow: false,
    loading: false,
    ruleForm: {
        title: '',
        description: '',
        ticketType: '',
        status: '',
        ticketLevel: '',
        assignedTo: '',
        dueDate: '',
        serialNumber:'',
        alertLogId: '' as any,
    }
});

// 工单详情对话框数据
const ticketDetailDialog = reactive({
    isShow: false,
    loading: false,
    isEdit: false,
    title: '工单详情',
    ruleForm: {
        ticketId: '',
        title: '',
        description: '',
        ticketType: '',
        status: '',
        ticketLevel: '',
        assignedTo: '',
        dueDate: '',
        alertLogId: '' as any,
        createTime: '',
        createBy: '',
    }
});

// 计算属性：判断是否为只读模式（初始加载时状态为3时）
const isReadOnlyMode = computed(() => {
    return initialStatus.value === 3;
});

// 禁用过去的日期
const disabledDate = (time: Date) => {
    return time.getTime() < Date.now() - 8.64e7; // 禁用昨天之前的日期
};
// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listAlertLog(state.tableData.param);
        state.tableData.data = response.data.rows as any;
        state.tableData.total = response.data.total;
        // console.log(state.tableData.data);
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};
/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        status: '',
        alertName: '',
        alertLevel: '',
    }
}
// 取消
const cancel = () => {
    dialogData.tableData.dialog.isShowDialog = false;
    dialogData.tableData.ruleForm.remark = '';
    dialogData.tableData.ruleForm.status = '';
    dialogData.tableData.ruleForm.createWorkOrder = false;
    dialogData.tableData.ruleForm.workOrderType = '';
    dialogData.tableData.ruleForm.assignedUser = '';
    dialogData.tableData.ruleForm.deadline = '';
    dialogData.tableData.ruleForm.selWorkOrder = false;
    dialogData.tableData.ruleForm.workOrderDetail = '';
    initialStatus.value = null; // 重置初始状态
}
/** 修改按钮操作 */
const handleUpdate = (row: any) => {
    // 保存当前告警行数据，用于工单明细回显
    currentAlertRow.value = row;

    // 重置表单数据
    dialogData.tableData.ruleForm.remark = '';
    dialogData.tableData.ruleForm.status = '';
    dialogData.tableData.ruleForm.createWorkOrder = false;
    dialogData.tableData.ruleForm.workOrderType = '';
    dialogData.tableData.ruleForm.assignedUser = '';
    dialogData.tableData.ruleForm.deadline = '';
    dialogData.tableData.ruleForm.selWorkOrder = false;
    dialogData.tableData.ruleForm.workOrderDetail = '';
    initialStatus.value = null; // 重置初始状态

    const alertLogId = row.alertLogId
    getAlertLog(alertLogId).then((response) => {
        dialogData.tableData.ruleForm = response.data.data;
        // 确保状态字段有默认值
        if (!dialogData.tableData.ruleForm.status) {
            dialogData.tableData.ruleForm.status = '';
        }

        // 记录初始加载时的状态
        initialStatus.value = dialogData.tableData.ruleForm.status;

        // 设置工单查询参数并加载工单列表
        ticketTableData.param.alertLogId = alertLogId;
        ticketTableData.param.pageNum = 1;
        getTicketTableData();

        dialogData.tableData.dialog.isShowDialog = true;
        // 根据初始处理状态设置对话框标题
        dialogData.tableData.dialog.title = initialStatus.value === 3 ? '查看告警日志' : '处理告警日志';
    });
}


// 提交
const SubmitForm = async (formEl: FormInstance | undefined) => {
    if (!formEl) return

    // 动态验证：如果选择创建工单，则工单类型、指派人员和截止日期必填
    if (dialogData.tableData.ruleForm.createWorkOrder) {
        if (!dialogData.tableData.ruleForm.workOrderType) {
            ElMessage.error('请选择工单类型');
            return;
        }
        if (!dialogData.tableData.ruleForm.assignedUser) {
            ElMessage.error('请选择指派人员');
            return;
        }
        if (!dialogData.tableData.ruleForm.deadline) {
            ElMessage.error('请选择截止日期');
            return;
        }
    }

    await formEl.validate(async (valid, fields) => {
        if (valid) {
            if (dialogData.tableData.ruleForm.alertLogId != '') {
                updateAlertLog(dialogData.tableData.ruleForm).then(() => {
                    ElMessage.success('处理成功');
                    if (dialogData.tableData.ruleForm.createWorkOrder) {
                        ElMessage.success('工单创建成功');
                    }
                    dialogData.tableData.dialog.isShowDialog = false;
                    getTableData();
                });
            } else {
                addAlertLog(dialogData.tableData.ruleForm).then(() => {
                    ElMessage.success('新增成功');
                    if (dialogData.tableData.ruleForm.createWorkOrder) {
                        ElMessage.success('工单创建成功');
                    }
                    dialogData.tableData.dialog.isShowDialog = false;
                    getTableData();
                });
            }

        } else {
            console.log('error submit!', fields)
        }
    })
};

// 获取用户列表数据
const getUserList = async () => {
    try {
        const response = await listUser({});
        if (response && response.data && response.data.rows) {
            userList.value = response.data.rows;
        }
    } catch (error) {
        console.error('获取用户列表失败:', error);
    }
};

// 获取状态数据
const getdictdata = async () => {
    try {
        alert_level_list.value = await dictStore.fetchDict('iot_alert_level')
        process_status_list.value = await dictStore.fetchDict('iot_process_status')
        work_order_type_list.value = await dictStore.fetchDict('iot_ticket_type')
        ticket_status_list.value = await dictStore.fetchDict('iot_ticket_status')
        ticket_level_list.value = await dictStore.fetchDict('iot_ticket_level')
        // 处理参数数据
    } catch (error) {
        console.error('获取参数数据失败:', error);
    }
};
/**格式化显示物模型**/
const formatDetail = (json: any) => {
    if (json == null || json == '') {
        return;
    }
    let item = JSON.parse(json);
    let result = 'id：<span style="color:#F56C6C">' + item.id + '</span><br />';
    if (item.name) {
    result += 'name：<span style="color:#F56C6C">' + item.name + '</span><br />';
    }
    result = result + 'value：<span style="color:#F56C6C">' + item.value + '</span><br />';
    result = result + 'remark：<span style="color:#F56C6C">' + item.remark + '</span>';
    return result;
}
// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
// 分页改变
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};

// 获取工单列表数据
const getTicketTableData = async () => {
    if (!ticketTableData.param.alertLogId) return;

    ticketTableData.loading = true;
    try {
        const response = await listTicket(ticketTableData.param);
        ticketTableData.data = response.data.rows as any;
        ticketTableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching ticket data:', error);
    } finally {
        setTimeout(() => {
            ticketTableData.loading = false;
        }, 500);
    }
};

// 工单分页改变
const onTicketSizeChange = (val: number) => {
    ticketTableData.param.pageSize = val;
    getTicketTableData();
};

// 工单分页改变
const onTicketCurrentChange = (val: number) => {
    ticketTableData.param.pageNum = val;
    getTicketTableData();
};

// 根据用户名获取用户昵称
const getUserName = (userName: any) => {
    if (!userName) return '';
    const user = userList.value.find(u => u.userName == userName);
    return user ? user.nickName : userName;
};

// 打开创建工单对话框
const openCreateTicketDialog = () => {
    createTicketDialog.ruleForm = {
        title: '',
        description: '',
        ticketType: '',
        status: '',
        ticketLevel: '',
        assignedTo: '',
        dueDate: '',
        alertLogId: dialogData.tableData.ruleForm.alertLogId,
        serialNumber: dialogData.tableData.ruleForm.serialNumber,
    };
    createTicketDialog.isShow = true;
};

// 处理工单详情（查看和编辑）
const handleTicketDetail = async (row: any) => {
    try {
        ticketDetailDialog.loading = true;
        const response = await getTicket(row.ticketId);
        const ticketData = response.data.data;

        // 填充表单数据，确保字典值为字符串类型以正确回显
        ticketDetailDialog.ruleForm = {
            ticketId: ticketData.ticketId,
            title: ticketData.title,
            description: ticketData.description || '',
            ticketType: String(ticketData.ticketType || ''),
            status: String(ticketData.status || ''),
            ticketLevel: String(ticketData.ticketLevel || ''),
            assignedTo: ticketData.assignedTo,
            dueDate: ticketData.dueDate,
            alertLogId: ticketData.alertLogId,
            createBy: ticketData.createBy,
            createTime: ticketData.createTime,
        };

        ticketDetailDialog.isEdit = false;
        ticketDetailDialog.title = '工单详情';
        ticketDetailDialog.isShow = true;
    } catch (error) {
        console.error('获取工单详情失败:', error);
        ElMessage.error('获取工单详情失败');
    } finally {
        ticketDetailDialog.loading = false;
    }
};

// 切换到编辑模式
const switchToEditMode = () => {
    ticketDetailDialog.isEdit = true;
    ticketDetailDialog.title = '编辑工单';
};

// 取消编辑
const cancelEdit = () => {
    ticketDetailDialog.isEdit = false;
    ticketDetailDialog.title = '工单详情';
};

// 提交工单更新
const submitTicketUpdate = async () => {
    if (!TicketDetailFormRef.value) return;

    await TicketDetailFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
            ticketDetailDialog.loading = true;
            try {
                await updateTicket(ticketDetailDialog.ruleForm);
                ElMessage.success('工单更新成功');
                ticketDetailDialog.isShow = false;
                // 刷新工单列表
                getTicketTableData();
            } catch (error) {
                console.error('更新工单失败:', error);
                ElMessage.error('更新工单失败');
            } finally {
                ticketDetailDialog.loading = false;
            }
        }
    });
};

// 删除工单
const handleDeleteTicket = (row: any) => {
    ElMessageBox.confirm('确定要删除这个工单吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
    }).then(async () => {
        try {
            await delTicket(row.ticketId);
            ElMessage.success('工单删除成功');
            // 刷新工单列表
            getTicketTableData();
        } catch (error) {
            console.error('删除工单失败:', error);
            ElMessage.error('删除工单失败');
        }
    }).catch(() => {
        // 用户取消删除
    });
};

// 提交创建工单
const submitCreateTicket = async () => {
    if (!CreateTicketFormRef.value) return;

    await CreateTicketFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
            createTicketDialog.loading = true;
            try {
                await addTicket(createTicketDialog.ruleForm);
                ElMessage.success('工单创建成功');
                createTicketDialog.isShow = false;
                // 刷新工单列表
                getTicketTableData();
            } catch (error) {
                console.error('创建工单失败:', error);
                ElMessage.error('创建工单失败');
            } finally {
                createTicketDialog.loading = false;
            }
        }
    });
};
// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata();
    getUserList();
});
</script>

<style scoped>
.work-order-detail {
    padding: 0;
    min-height: auto;
}

.work-order-detail pre {
    margin: 0;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    color: #606266;
    white-space: pre-wrap;
    word-wrap: break-word;
    background: none;
    border: none;
}
</style>
