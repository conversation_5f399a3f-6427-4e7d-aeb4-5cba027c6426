<template>
    <div class="system-dic-container layout-padding">
        <el-card shadow="hover" class="layout-padding-auto">
            <div class="system-user-search mb15">
                <el-form size="default" :inline="true" label-width="80px">
                    <el-form-item label="工单标题" prop="title">
                        <el-input v-model="state.tableData.param.title" clearable size="default"
                            placeholder="请输入工单标题" style="width: 240px" />
                    </el-form-item>
                    <el-form-item label="工单类型" prop="type">
                        <el-select v-model="state.tableData.param.ticketType" placeholder="请选择工单类型" clearable
                            size="default" style="width: 240px">
                            <el-option v-for="dict in work_order_type_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="parseInt(dict.dictValue)" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="工单级别" prop="level">
                        <el-select v-model="state.tableData.param.ticketLevel" placeholder="请选择工单级别" clearable size="default"
                            style="width: 180px">
                            <el-option v-for="dict in ticket_level_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="parseInt(dict.dictValue)" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="指派人员" prop="assignedUser">
                        <el-select v-model="state.tableData.param.assignedTo" placeholder="请选择指派人员" clearable
                            size="default" style="width: 180px">
                            <el-option v-for="user in userList" :key="user.userName" :label="user.nickName"
                                :value="user.userName" />
                        </el-select>
                    </el-form-item>
                    <el-form-item label="工单状态" prop="status">
                        <el-select v-model="state.tableData.param.status" placeholder="请选择工单状态" clearable size="default"
                            style="width: 180px">
                            <el-option v-for="dict in ticket_status_list" :key="dict.dictValue" :label="dict.dictLabel"
                                :value="parseInt(dict.dictValue)" />
                        </el-select>
                    </el-form-item>
                    <el-form-item>
                        <el-button size="default" type="primary" class="ml10" @click="getTableData">
                            <el-icon>
                                <ele-Search />
                            </el-icon>
                            查询
                        </el-button>
                        <el-button size="default" @click="resetQuery">
                            <el-icon><ele-Refresh /></el-icon>
                            重置
                        </el-button>
                    </el-form-item>
                </el-form>
            </div>
            <el-table :data="state.tableData.data" v-loading="state.tableData.loading" border style="width: 100%"
                :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                <el-table-column label="工单标题" align="center" prop="title" />
                <el-table-column label="工单类型" align="center" prop="type" width="120">
                    <template #default="scope">
                        <dict-tag :options="work_order_type_list" :value="scope.row.ticketType" />
                    </template>
                </el-table-column>
                <el-table-column label="工单级别" align="center" prop="level" width="120">
                    <template #default="scope">
                        <dict-tag :options="ticket_level_list" :value="scope.row.ticketLevel" />
                    </template>
                </el-table-column>
                <el-table-column label="指派人员" align="center" prop="assignedUser" width="120">
                    <template #default="scope">
                        <span>{{ getUserName(scope.row.assignedTo) }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="工单状态" align="center" prop="status" width="120">
                    <template #default="scope">
                        <dict-tag :options="ticket_status_list" :value="scope.row.status" />
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createTime" width="170">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="截止时间" align="center" prop="deadline" width="170">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.dueDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="150">
                    <template #default="scope">
                        <el-button size="default" text type="primary" @click="handleView(scope.row)"
                            v-auths="['iot:ticket:query']">
                            <el-icon><View /></el-icon>查看
                        </el-button>
                        <el-button size="default" text type="primary" @click="handleUpdate(scope.row)"
                            v-auths="['iot:ticket:edit']">
                            <el-icon><Edit /></el-icon>编辑
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-pagination @size-change="onHandleSizeChange" @current-change="onHandleCurrentChange" class="mt15"
                style="justify-content: flex-end;" :pager-count="5" :page-sizes="[10, 20, 30]"
                v-model:current-page="state.tableData.param.pageNum" background
                v-model:page-size="state.tableData.param.pageSize" layout="total, sizes, prev, pager, next, jumper"
                :total="state.tableData.total">
            </el-pagination>
        </el-card>

        <!-- 工单详情对话框 -->
        <el-dialog style="position: absolute; top: 100px;" :title="dialogData.tableData.dialog.title"
            v-model="dialogData.tableData.dialog.isShowDialog" width="800px" append-to-body>
            <el-form ref="DialogFormRef" :model="dialogData.tableData.ruleForm" :rules="rules" label-width="100px">
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="工单标题" prop="title">
                            <el-input v-model="dialogData.tableData.ruleForm.title" placeholder="请输入工单标题"
                                :disabled="isReadOnlyMode" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="工单类型" prop="ticketType">
                            <el-select v-model="dialogData.tableData.ruleForm.ticketType" placeholder="请选择工单类型"
                                style="width: 100%" size="default" :disabled="isReadOnlyMode">
                                <el-option v-for="dict in work_order_type_list" :key="dict.dictValue" :label="dict.dictLabel"
                                    :value="parseInt(dict.dictValue)" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="工单级别" prop="ticketLevel">
                            <el-select v-model="dialogData.tableData.ruleForm.ticketLevel" placeholder="请选择工单级别"
                                style="width: 100%" size="default" :disabled="isReadOnlyMode">
                                <el-option v-for="dict in ticket_level_list" :key="dict.dictValue" :label="dict.dictLabel"
                                    :value="parseInt(dict.dictValue)" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="指派人员" prop="assignedTo">
                            <el-select v-model="dialogData.tableData.ruleForm.assignedTo" placeholder="请选择指派人员"
                                style="width: 100%" size="default" :disabled="isReadOnlyMode">
                                <el-option v-for="user in userList" :key="user.userName" :label="user.nickName"
                                    :value="user.userName" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="12">
                        <el-form-item label="工单状态" prop="status">
                            <el-select v-model="dialogData.tableData.ruleForm.status" placeholder="请选择工单状态"
                                style="width: 100%" size="default" :disabled="isReadOnlyMode">
                                <el-option v-for="dict in ticket_status_list" :key="dict.dictValue" :label="dict.dictLabel"
                                    :value="parseInt(dict.dictValue)" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="截止时间" prop="dueDate">
                            <el-date-picker v-model="dialogData.tableData.ruleForm.dueDate" type="datetime"
                                placeholder="请选择截止时间" style="width: 100%" :disabled="isReadOnlyMode" />
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-form-item label="工单描述" prop="description">
                    <el-input v-model="dialogData.tableData.ruleForm.description" type="textarea" :rows="4"
                        placeholder="请输入工单描述" :disabled="isReadOnlyMode" />
                </el-form-item>
            </el-form>

            <!-- 标签页区域 -->
            <div class="tabs-section" style="margin-top: 20px;">
                <el-tabs v-model="activeTabName" type="border-card">
                    <!-- 更改历史标签页 -->
                    <el-tab-pane label="更改历史" name="history">
                        <el-table :data="historyTableData.data" v-loading="historyTableData.loading"
                            border style="width: 100%" size="small"
                            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                            <template #empty>
                                <div style="padding: 20px; color: #909399;">
                                    <el-icon size="48" style="margin-bottom: 10px;"><ele-DocumentCopy /></el-icon>
                                    <div>暂无操作记录</div>
                                </div>
                            </template>
                            <el-table-column label="操作时间" align="center" prop="operateTime" width="150">
                                <template #default="scope">
                                    <span>{{ parseTime(scope.row.operateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作人员" align="center" prop="operateUser" width="120">
                                <template #default="scope">
                                    <span>{{ scope.row.operateUser }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作类型" align="center" prop="operateType" width="100">
                                <template #default="scope">
                                    <el-tag :type="getOperateTypeTag(scope.row.operateType)">
                                        {{ getOperateTypeName(scope.row.operateType) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作内容" align="left" prop="operateContent" min-width="350">
                                <template #default="scope">
                                    <div style="max-height: 120px; overflow-y: auto; line-height: 1.6; white-space: pre-line; font-size: 13px;">
                                        <div v-html="formatOperateContent(scope.row.operateContent)"></div>
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作IP" align="center" prop="operIp" width="120">
                                <template #default="scope">
                                    <span>{{ scope.row.originalData?.operIp || '-' }}</span>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 修改历史分页 -->
                        <el-pagination
                            v-if="historyTableData.total > 0"
                            @size-change="onHistorySizeChange"
                            @current-change="onHistoryCurrentChange"
                            class="mt15"
                            style="justify-content: flex-end;"
                            :pager-count="5"
                            :page-sizes="[5, 10, 20]"
                            v-model:current-page="historyTableData.param.pageNum"
                            background
                            v-model:page-size="historyTableData.param.pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="historyTableData.total">
                        </el-pagination>
                    </el-tab-pane>

                    <!-- 工时记录标签页 -->
                    <el-tab-pane label="耗时" name="worklog">
                        <!-- 总耗时统计 -->
                        <div class="total-hours-summary" style="margin-bottom: 15px; padding: 12px; background-color: #f8f9fa; border-radius: 4px; border-left: 4px solid #409EFF;">
                            <div style="display: flex; align-items: center;">
                                <el-icon size="18" style="color: #409EFF; margin-right: 8px;"><Clock /></el-icon>
                                <span style="font-size: 14px; font-weight: 600; color: #303133;">
                                    一共耗时：<span style="color: #409EFF; font-size: 16px;">{{ totalWorkHours }}</span> 小时
                                </span>
                            </div>
                        </div>

                        <el-table :data="workLogTableData.data" v-loading="workLogTableData.loading"
                            border style="width: 100%" size="small"
                            :header-cell-style="{ background: '#f8f8f9', color: '#515a6e' }">
                            <template #empty>
                                <div style="padding: 20px; color: #909399;">
                                    <el-icon size="48" style="margin-bottom: 10px;"><ele-Clock /></el-icon>
                                    <div>暂无工时记录</div>
                                </div>
                            </template>
                            <el-table-column label="工作日期" align="center" prop="logTime" width="120">
                                <template #default="scope">
                                    <span>{{ scope.row.logTime }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="工作时长" align="center" prop="hours" width="100">
                                <template #default="scope">
                                    <span>{{ scope.row.hours }}小时</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="工作内容" align="left" prop="remark" min-width="200">
                                <template #default="scope">
                                    <div style="max-height: 60px; overflow-y: auto; line-height: 1.4;">
                                        {{ scope.row.remark }}
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="登记人" align="center" prop="createBy" width="100">
                                <template #default="scope">
                                    <span>{{ scope.row.createBy }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="登记时间" align="center" prop="createTime" width="150">
                                <template #default="scope">
                                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" align="center" width="120">
                                <template #default="scope">
                                    <el-button size="small" text type="primary" @click="handleEditWorkLog(scope.row)">
                                        <el-icon><Edit /></el-icon>修改
                                    </el-button>
                                    <el-button size="small" text type="danger" @click="handleDeleteWorkLog(scope.row)">
                                        <el-icon><Delete /></el-icon>删除
                                    </el-button>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 工时记录分页 -->
                        <el-pagination
                            v-if="workLogTableData.total > 0"
                            @size-change="onWorkLogSizeChange"
                            @current-change="onWorkLogCurrentChange"
                            class="mt15"
                            style="justify-content: flex-end;"
                            :pager-count="5"
                            :page-sizes="[5, 10, 20]"
                            v-model:current-page="workLogTableData.param.pageNum"
                            background
                            v-model:page-size="workLogTableData.param.pageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="workLogTableData.total">
                        </el-pagination>
                    </el-tab-pane>
                </el-tabs>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="dialogData.tableData.dialog.isShowDialog = false">取消</el-button>
                    <el-button type="success" @click="openWorkLogDialog" v-if="dialogData.tableData.ruleForm.ticketId">
                        <el-icon><Clock /></el-icon>登记工时
                    </el-button>
                    <el-button type="primary" @click="submitForm" v-if="!isReadOnlyMode">确定</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 工时登记对话框 -->
        <el-dialog style="position: absolute; top: 100px;" :title="workLogDialog.title"
            v-model="workLogDialog.isShowDialog" width="500px" append-to-body>
            <el-form ref="WorkLogFormRef" :model="workLogDialog.ruleForm" :rules="workLogRules" label-width="100px">
                <el-form-item label="工作日期" prop="logTime" required>
                    <el-date-picker v-model="workLogDialog.ruleForm.logTime" type="date"
                        placeholder="请选择工作日期" style="width: 100%" value-format="YYYY-MM-DD" />
                </el-form-item>
                <el-form-item label="工作时长" prop="hours" required>
                    <el-input-number v-model="workLogDialog.ruleForm.hours" :min="1" :max="8" :step="1"
                        :precision="0" placeholder="请输入工作时长" style="width: 200px" @change="handleHoursChange" />
                    <span style="margin-left: 8px; color: #909399;">小时</span>
                </el-form-item>
                <el-form-item prop="remark" required>
                    <template #label>
                        <span style="margin-left: 4px;">工作内容</span>
                    </template>
                    <el-input v-model="workLogDialog.ruleForm.remark" type="textarea" :rows="4"
                        placeholder="请输入工作内容描述" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="workLogDialog.isShowDialog = false">取消</el-button>
                    <el-button type="primary" @click="submitWorkLog">确定</el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts" name="">
import { reactive, onMounted, ref, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useDictStore } from '/@/stores/dictStore';  // 导入 Pinia store
import DictTag from '/@/components/DictTag/index.vue'
import { parseTime } from '/@/utils/next'
import { listTicket, getTicket, updateTicket } from '/@/api/iot/ticket';
import { listUser } from "/@/api/system/user";
import { list as listOperlog } from '/@/api/monitor/operlog';
import { listTicketLog, updateTicketLog, addTicketLog, delTicketLog } from '/@/api/iot/ticketLog';

const dictStore = useDictStore();  // 使用 Pinia store

// 引入组件
interface Option {
    dictValue: string;
    dictLabel: string;
    listClass: string;
    cssClass: string;
}

// 定义变量内容
const state = reactive({
    tableData: {
        data: [],
        total: 0,
        loading: false,
        param: {
            pageNum: 1,
            pageSize: 10,
            title: '',
            ticketType: '',
            ticketLevel: '',
            assignedTo: '',
            status: '',
        },
    },
});

const dialogData = reactive({
    tableData: {
        ruleForm: {
            ticketId: '' as any,
            title: '',
            ticketType: '',
            ticketLevel: '',
            assignedTo: '',
            status: '',
            dueDate: '' as any,
            description: '',
            serialNumber: '',
        },
        dialog: {
            isShowDialog: false,
            title: '',
        },
    },
})

// 修改历史表格数据
const historyTableData = reactive({
    data: [],
    total: 0,
    loading: false,
    param: {
        pageNum: 1,
        pageSize: 5,
        ticketId: '' as any,
    },
});

// 工时登记对话框数据
const workLogDialog = reactive({
    isShowDialog: false,
    isEdit: false,
    title: '登记工时',
    ruleForm: {
        logId: '' as any,
        ticketId: '' as any,
        logTime: '',
        hours: 1,
        remark: '',
    },
});

// 工时记录表格数据
const workLogTableData = reactive({
    data: [],
    total: 0,
    loading: false,
    param: {
        pageNum: 1,
        pageSize: 5,
        ticketId: '' as any,
    },
});

// 表单验证规则
const rules = reactive({
    title: [{ required: true, message: '工单标题不能为空', trigger: 'blur' }],
    ticketType: [{ required: true, message: '工单类型不能为空', trigger: 'change' }],
    ticketLevel: [{ required: true, message: '工单级别不能为空', trigger: 'change' }],
    assignedTo: [{ required: true, message: '指派人员不能为空', trigger: 'change' }],
    status: [{ required: true, message: '工单状态不能为空', trigger: 'change' }],
});

// 自定义验证：检查一天内工作时长不超过8小时
const validateDailyHours = async (rule: any, value: number, callback: any) => {
    if (!value || !workLogDialog.ruleForm.logTime) {
        callback();
        return;
    }

    try {
        // 获取当前日期的所有工时记录
        const response = await listTicketLog({
            ticketId: workLogDialog.ruleForm.ticketId,
            logTime: workLogDialog.ruleForm.logTime,
            pageNum: 1,
            pageSize: 100
        });

        // 计算当天已有的工作时长（排除当前编辑的记录）
        let totalHours = 0;
        if (response.data && response.data.rows) {
            totalHours = response.data.rows
                .filter((item: any) =>
                    item.logTime === workLogDialog.ruleForm.logTime &&
                    item.logId !== workLogDialog.ruleForm.logId
                )
                .reduce((sum: number, item: any) => sum + (item.hours || 0), 0);
        }

        // 检查总时长是否超过8小时
        if (totalHours + value > 8) {
            callback(new Error(`当天工作时长不能超过8小时，当前已登记${totalHours}小时，最多还能登记${8 - totalHours}小时`));
        } else {
            callback();
        }
    } catch (error) {
        console.error('验证工作时长失败:', error);
        callback();
    }
};

// 工时登记表单验证规则
const workLogRules = reactive({
    logTime: [{ required: true, message: '工作日期不能为空', trigger: 'change' }],
    hours: [
        { required: true, message: '工作时长不能为空', trigger: 'blur' },
        { type: 'number', min: 1, max: 8, message: '工作时长必须在1-8小时之间', trigger: 'blur' },
        { validator: validateDailyHours, trigger: 'blur' }
    ],
    remark: [{ required: true, message: '工作内容不能为空', trigger: 'blur' }],
});

// 定义变量内容
const work_order_type_list = ref<Option[]>([]);
const ticket_status_list = ref<Option[]>([]);
const ticket_level_list = ref<Option[]>([]);
const userList = ref<any[]>([]);
const DialogFormRef = ref();
const WorkLogFormRef = ref();

// 当前操作模式（查看/编辑）
const currentMode = ref<'view' | 'edit'>('view');

// 当前激活的标签页
const activeTabName = ref('history');

// 计算属性：是否为只读模式
const isReadOnlyMode = computed(() => currentMode.value === 'view');

// 计算属性：总工作时长
const totalWorkHours = computed(() => {
    if (!workLogTableData.data || workLogTableData.data.length === 0) {
        return 0;
    }
    return workLogTableData.data.reduce((total: number, item: any) => {
        return total + (item.hours || 0);
    }, 0);
});

// 分页改变
const onHandleSizeChange = (val: number) => {
    state.tableData.param.pageSize = val;
    getTableData();
};
const onHandleCurrentChange = (val: number) => {
    state.tableData.param.pageNum = val;
    getTableData();
};

// 初始化表格数据
const getTableData = async () => {
    state.tableData.loading = true;
    try {
        const response = await listTicket(state.tableData.param);
        state.tableData.data = response.data.rows as any;
        state.tableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching table data:', error);
    } finally {
        setTimeout(() => {
            state.tableData.loading = false;
        }, 500);
    }
};

/** 重置按钮操作 */
const resetQuery = () => {
    state.tableData.param = {
        pageNum: 1,
        pageSize: 10,
        title: '',
        ticketType: '',
        ticketLevel: '',
        assignedTo: '',
        status: '',
    }
    getTableData();
}

// 根据用户名获取用户昵称
const getUserName = (userName: any) => {
    if (!userName) return '';
    const user = userList.value.find(u => u.userName == userName || u.userName === String(userName));
    return user ? user.nickName : String(userName);
};

// 查看工单详情
const handleView = (row: any) => {
    currentMode.value = 'view';
    const ticketId = row.ticketId;
    getTicket(ticketId).then((response) => {
        const ticketData = response.data.data;
        dialogData.tableData.ruleForm = ticketData;

        // 设置修改历史查询参数并加载修改历史
        historyTableData.param.ticketId = ticketId;
        historyTableData.param.pageNum = 1;
        getHistoryTableData();

        // 设置工时记录查询参数并加载工时记录
        workLogTableData.param.ticketId = ticketId;
        workLogTableData.param.pageNum = 1;
        getWorkLogTableData();

        dialogData.tableData.dialog.isShowDialog = true;
        dialogData.tableData.dialog.title = '查看工单详情';
    });
}

// 编辑工单
const handleUpdate = (row: any) => {
    currentMode.value = 'edit';
    const ticketId = row.ticketId;
    getTicket(ticketId).then((response) => {
        const ticketData = response.data.data;
        console.log('编辑工单数据:', ticketData);

        dialogData.tableData.ruleForm = ticketData;

        // 设置修改历史查询参数并加载修改历史
        historyTableData.param.ticketId = ticketId;
        historyTableData.param.pageNum = 1;
        getHistoryTableData();

        // 设置工时记录查询参数并加载工时记录
        workLogTableData.param.ticketId = ticketId;
        workLogTableData.param.pageNum = 1;
        getWorkLogTableData();

        dialogData.tableData.dialog.isShowDialog = true;
        dialogData.tableData.dialog.title = '编辑工单';
    });
}

// 提交表单
const submitForm = async () => {
    if (!DialogFormRef.value) return;
    await DialogFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
            try {
                await updateTicket(dialogData.tableData.ruleForm);
                ElMessage.success('工单更新成功');
                dialogData.tableData.dialog.isShowDialog = false;
                getTableData();
            } catch (error) {
                console.error('更新工单失败:', error);
                ElMessage.error('更新工单失败');
            }
        }
    });
};

// 获取用户列表数据
const getUserList = async () => {
    try {
        const response = await listUser({});
        if (response && response.data && response.data.rows) {
            userList.value = response.data.rows;
        }
    } catch (error) {
        console.error('获取用户列表失败:', error);
    }
};

// 获取字典数据
const getdictdata = async () => {
    try {
        work_order_type_list.value = await dictStore.fetchDict('iot_ticket_type')
        ticket_status_list.value = await dictStore.fetchDict('iot_ticket_status')
        ticket_level_list.value = await dictStore.fetchDict('iot_ticket_level')
    } catch (error) {
        console.error('获取字典数据失败:', error);
    }
};

// 获取修改历史列表数据
const getHistoryTableData = async () => {
    if (!historyTableData.param.ticketId) return;

    historyTableData.loading = true;
    try {
        // 查询操作日志，筛选工单相关的操作
        const queryParams = {
            pageNum: 1, // 先获取所有数据，然后在前端过滤
            pageSize: 100, // 获取更多数据以便筛选
            title: '工单', // 筛选标题包含"工单"的操作
            operName: undefined, // 操作人员
            businessType: undefined, // 业务类型
            status: '', // 状态为空，获取所有状态的记录
            orderByColumn: 'operTime', // 按操作时间排序
            isAsc: 'desc' // 降序排列，最新的在前面
        };

        const response = await listOperlog(queryParams);

        // 过滤出当前工单相关的操作记录
        const ticketId = historyTableData.param.ticketId;
        const filteredData = response.data.rows.filter((item: any) => {
            // 检查是否是工单相关的操作
            const isTicketOperation = item.operUrl && item.operUrl.includes('/iot/ticket');

            // 检查操作参数中是否包含当前工单ID
            let hasTicketId = false;
            if (item.operParam) {
                // 支持多种格式的工单ID匹配
                hasTicketId = item.operParam.includes(`"ticketId":${ticketId}`) ||
                             item.operParam.includes(`"ticketId":"${ticketId}"`) ||
                             item.operUrl.includes(`/iot/ticket/${ticketId}`);
            }

            return isTicketOperation && hasTicketId;
        });

        // 按时间排序，确保能正确对比前后变更
        const sortedData = filteredData.sort((a: any, b: any) =>
            new Date(a.operTime).getTime() - new Date(b.operTime).getTime()
        );

        // 转换数据格式以适配表格显示，并过滤掉没有实际变更的记录
        const transformedData = sortedData.map((item: any) => {
            const operateContent = generateOperateContent(item, sortedData);
            const hasChanges = hasActualChanges(item, operateContent);

            console.log('操作记录处理:', {
                method: item.requestMethod,
                title: item.title,
                operateContent: operateContent,
                hasChanges: hasChanges
            });

            return {
                operateTime: item.operTime,
                operateUser: item.operName,
                operateType: getOperateTypeFromTitle(item.title),
                operateContent: operateContent,
                remark: item.title,
                originalData: item, // 保留原始数据
                hasChanges: hasChanges // 标记是否有实际变更
            };
        }).filter(item => item.hasChanges); // 只保留有实际变更的记录

        // 重新按时间倒序排列，最新的在前面
        transformedData.reverse();

        // 实现前端分页
        const startIndex = (historyTableData.param.pageNum - 1) * historyTableData.param.pageSize;
        const endIndex = startIndex + historyTableData.param.pageSize;
        const paginatedData = transformedData.slice(startIndex, endIndex);

        historyTableData.data = paginatedData as any;
        historyTableData.total = transformedData.length;

    } catch (error) {
        console.error('Error fetching history data:', error);
        // 如果API调用失败，使用模拟数据
        const mockData = [
            {
                operateTime: new Date().toISOString(),
                operateUser: '管理员',
                operateType: 'create',
                operateContent: '创建工单',
                remark: '初始创建'
            },
            {
                operateTime: new Date(Date.now() - 3600000).toISOString(),
                operateUser: '张三',
                operateType: 'update',
                operateContent: '修改工单状态：待处理 → 处理中',
                remark: '开始处理'
            }
        ];
        historyTableData.data = mockData as any;
        historyTableData.total = mockData.length;
    } finally {
        setTimeout(() => {
            historyTableData.loading = false;
        }, 500);
    }
};

// 修改历史分页改变
const onHistorySizeChange = (val: number) => {
    historyTableData.param.pageSize = val;
    getHistoryTableData();
};

// 修改历史分页改变
const onHistoryCurrentChange = (val: number) => {
    historyTableData.param.pageNum = val;
    getHistoryTableData();
};

// 打开工时登记对话框
const openWorkLogDialog = () => {
    // 重置表单数据
    workLogDialog.isEdit = false;
    workLogDialog.title = '登记工时';
    workLogDialog.ruleForm = {
        logId: '',
        ticketId: dialogData.tableData.ruleForm.ticketId,
        logTime: new Date().toISOString().split('T')[0], // 默认今天
        hours: 1,
        remark: '',
    };

    workLogDialog.isShowDialog = true;
};

// 获取工时记录列表数据
const getWorkLogTableData = async () => {
    if (!workLogTableData.param.ticketId) return;

    workLogTableData.loading = true;
    try {
        const response = await listTicketLog(workLogTableData.param);
        workLogTableData.data = response.data.rows as any;
        workLogTableData.total = response.data.total;
    } catch (error) {
        console.error('Error fetching work log data:', error);
        // 如果API调用失败，使用空数据
        workLogTableData.data = [];
        workLogTableData.total = 0;
    } finally {
        setTimeout(() => {
            workLogTableData.loading = false;
        }, 500);
    }
};

// 工时记录分页改变
const onWorkLogSizeChange = (val: number) => {
    workLogTableData.param.pageSize = val;
    getWorkLogTableData();
};

// 工时记录分页改变
const onWorkLogCurrentChange = (val: number) => {
    workLogTableData.param.pageNum = val;
    getWorkLogTableData();
};

// 提交工时登记
const submitWorkLog = async () => {
    if (!WorkLogFormRef.value) return;
    await WorkLogFormRef.value.validate(async (valid: boolean) => {
        if (valid) {
            try {
                if (workLogDialog.isEdit) {
                    // 编辑模式
                    await updateTicketLog(workLogDialog.ruleForm);
                    ElMessage.success('工时修改成功');
                } else {
                    // 新增模式
                    await addTicketLog(workLogDialog.ruleForm);
                    ElMessage.success('工时登记成功');
                }

                // 关闭工时登记对话框
                workLogDialog.isShowDialog = false;

                // 重置表单
                workLogDialog.ruleForm = {
                    logId: '',
                    ticketId: dialogData.tableData.ruleForm.ticketId,
                    logTime: new Date().toISOString().split('T')[0],
                    hours: 1,
                    remark: '',
                };

                // 刷新主对话框中的工时记录列表
                getWorkLogTableData();
            } catch (error) {
                console.error(workLogDialog.isEdit ? '修改工时失败:' : '登记工时失败:', error);
                ElMessage.error(workLogDialog.isEdit ? '修改工时失败' : '登记工时失败');
            }
        }
    });
};

// 工作时长变化处理
const handleHoursChange = async (value: number) => {
    if (!value || !workLogDialog.ruleForm.logTime) return;

    try {
        // 获取当前日期的所有工时记录
        const response = await listTicketLog({
            ticketId: workLogDialog.ruleForm.ticketId,
            logTime: workLogDialog.ruleForm.logTime,
            pageNum: 1,
            pageSize: 100
        });

        // 计算当天已有的工作时长（排除当前编辑的记录）
        let totalHours = 0;
        if (response.data && response.data.rows) {
            totalHours = response.data.rows
                .filter((item: any) =>
                    item.logTime === workLogDialog.ruleForm.logTime &&
                    item.logId !== workLogDialog.ruleForm.logId
                )
                .reduce((sum: number, item: any) => sum + (item.hours || 0), 0);
        }

        // 检查总时长是否超过8小时
        if (totalHours + value > 8) {
            ElMessage.warning(`当天工作时长不能超过8小时，当前已登记${totalHours}小时，最多还能登记${8 - totalHours}小时`);
            workLogDialog.ruleForm.hours = Math.max(1, 8 - totalHours);
        }
    } catch (error) {
        console.error('检查工作时长失败:', error);
    }
};

// 编辑工时记录
const handleEditWorkLog = (row: any) => {
    workLogDialog.isEdit = true;
    workLogDialog.title = '修改工时';
    workLogDialog.ruleForm = {
        logId: row.logId,
        ticketId: row.ticketId,
        logTime: row.logTime,
        hours: row.hours,
        remark: row.remark,
    };
    workLogDialog.isShowDialog = true;
};

// 删除工时记录
const handleDeleteWorkLog = async (row: any) => {
    try {
        await ElMessageBox.confirm('确定要删除这条工时记录吗？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        });

        await delTicketLog(row.logId);
        ElMessage.success('删除成功');
        getWorkLogTableData();
    } catch (error) {
        if (error !== 'cancel') {
            console.error('删除工时记录失败:', error);
            ElMessage.error('删除失败');
        }
    }
};

// 获取操作类型标签样式
const getOperateTypeTag = (operateType: string) => {
    const typeMap: { [key: string]: string } = {
        'create': 'success',
        'update': 'warning',
        'delete': 'danger',
        'assign': 'info',
        'complete': 'success'
    };
    return typeMap[operateType] || 'info';
};

// 获取操作类型名称
const getOperateTypeName = (operateType: string) => {
    const nameMap: { [key: string]: string } = {
        'create': '创建',
        'update': '修改',
        'delete': '删除',
        'assign': '分配',
        'complete': '完成'
    };
    return nameMap[operateType] || operateType;
};

// 根据操作标题获取操作类型
const getOperateTypeFromTitle = (title: string) => {
    if (title.includes('新增') || title.includes('创建')) return 'create';
    if (title.includes('修改') || title.includes('编辑')) return 'update';
    if (title.includes('删除')) return 'delete';
    if (title.includes('分配') || title.includes('指派')) return 'assign';
    if (title.includes('完成')) return 'complete';
    return 'update'; // 默认为修改
};

// 生成操作内容描述
const generateOperateContent = (operlogItem: any, allOperations: any[] = []) => {
    const { title, operParam, requestMethod } = operlogItem;

    try {
        if (operParam) {
            const params = JSON.parse(operParam);
            let content = '';

            // 根据不同的操作类型生成不同的描述
            if (requestMethod === 'POST') {
                const details = [];
              if (params.title !== undefined) details.push(`工单标题：${getTicketTypeLabel(params.title)}`);
                if (params.ticketType !== undefined) details.push(`工单类型：${getTicketTypeLabel(params.ticketType)}`);
                if (params.ticketLevel !== undefined) details.push(`工单级别：${getTicketLevelLabel(params.ticketLevel)}`);
                if (params.assignedTo) details.push(`指派人员：${params.assignedTo}`);
                if (params.status !== undefined) details.push(`工单状态：${getTicketStatusLabel(params.status)}`);
                if (params.dueDate) details.push(`截止时间：${formatDate(params.dueDate)}`);
                if (details.length > 0) {
                    content += `\n新增内容：${details.join('; ')}`;
                }
            } else if (requestMethod === 'PUT') {
                // 获取前一次的操作记录来对比变更
                const previousOperation = getPreviousOperation(operlogItem, allOperations);
                const changes = getFieldChanges(params, previousOperation);

                if (changes.length > 0) {
                    content += `\n修改内容：\n${changes.join('\n')}`;
                }
                // 如果没有检测到字段变更，只显示基本的操作信息，不添加额外备注
            } else if (requestMethod === 'DELETE') {
                content = `删除工单：${params.title || params.ticketId || ''}`;
            }

            return content;
        }
    } catch (error) {
        console.error('解析操作参数失败:', error);
    }

    return title; // 如果解析失败，返回原始标题
};

// 获取前一次操作记录
const getPreviousOperation = (currentOperation: any, allOperations: any[]) => {
    const currentTime = new Date(currentOperation.operTime).getTime();
    const ticketId = extractTicketId(currentOperation.operParam);

    // 找到同一工单的前一次操作
    const previousOps = allOperations
        .filter(op => {
            const opTime = new Date(op.operTime).getTime();
            const opTicketId = extractTicketId(op.operParam);
            return opTicketId === ticketId && opTime < currentTime && op.requestMethod === 'PUT';
        })
        .sort((a, b) => new Date(b.operTime).getTime() - new Date(a.operTime).getTime());

    return previousOps.length > 0 ? previousOps[0] : null;
};

// 提取工单ID
const extractTicketId = (operParam: string) => {
    try {
        const params = JSON.parse(operParam);
        return params.ticketId;
    } catch {
        return null;
    }
};

// 获取字段变更详情
const getFieldChanges = (currentParams: any, previousOperation: any) => {
    const changes: string[] = [];

    if (!previousOperation) {
        return changes; // 如果没有前一次操作，返回空数组
    }

    try {
        const previousParams = JSON.parse(previousOperation.operParam);

        // 检查各个字段的变更，只有真正发生变更的字段才添加到变更列表

        // 工单标题变更
        if (isFieldChanged(currentParams.title, previousParams.title)) {
            const oldValue = previousParams.title || '空';
            const newValue = currentParams.title || '空';
            changes.push(`"工单标题"从"${oldValue}"变更为"${newValue}"`);
        }

        // 工单状态变更
        if (isFieldChanged(currentParams.status, previousParams.status)) {
            const oldStatus = getTicketStatusLabel(previousParams.status);
            const newStatus = getTicketStatusLabel(currentParams.status);
            changes.push(`"工单状态"从"${oldStatus}"变更为"${newStatus}"`);
        }

        // 指派人员变更
        if (isFieldChanged(currentParams.assignedTo, previousParams.assignedTo)) {
            const oldAssigned = previousParams.assignedTo || '未指派';
            const newAssigned = currentParams.assignedTo || '未指派';
            changes.push(`"指派人员"字段从"${oldAssigned}"变更为"${newAssigned}"`);
        }

        // 工单级别变更
        if (isFieldChanged(currentParams.ticketLevel, previousParams.ticketLevel)) {
            const oldLevel = getTicketLevelLabel(previousParams.ticketLevel);
            const newLevel = getTicketLevelLabel(currentParams.ticketLevel);
            changes.push(`"工单级别"从"${oldLevel}"变更为"${newLevel}"`);
        }

        // 工单类型变更
        if (isFieldChanged(currentParams.ticketType, previousParams.ticketType)) {
            const oldType = getTicketTypeLabel(previousParams.ticketType);
            const newType = getTicketTypeLabel(currentParams.ticketType);
            changes.push(`"工单类型"从"${oldType}"变更为"${newType}"`);
        }

        // 截止时间变更
        if (isFieldChanged(currentParams.dueDate, previousParams.dueDate)) {
            const oldDate = previousParams.dueDate ? formatDate(previousParams.dueDate) : '未设置';
            const newDate = currentParams.dueDate ? formatDate(currentParams.dueDate) : '未设置';
            changes.push(`"截止时间"从"${oldDate}"变更为"${newDate}"`);
        }

        // 工单描述变更
        if (isFieldChanged(currentParams.description, previousParams.description)) {
            const oldDesc = previousParams.description ?
                (previousParams.description.length > 30 ? previousParams.description.substring(0, 30) + '...' : previousParams.description) : '空';
            const newDesc = currentParams.description ?
                (currentParams.description.length > 30 ? currentParams.description.substring(0, 30) + '...' : currentParams.description) : '空';
            changes.push(`"工单描述"字段"从"${oldDesc}"变更为"${newDesc}"`);
        }

    } catch (error) {
        console.error('对比字段变更失败:', error);
    }

    return changes;
};

// 判断字段是否发生变更
const isFieldChanged = (currentValue: any, previousValue: any) => {
    // 处理 undefined、null、空字符串的情况
    const normalizeValue = (value: any) => {
        if (value === undefined || value === null || value === '') {
            return null;
        }
        return value;
    };

    const normalizedCurrent = normalizeValue(currentValue);
    const normalizedPrevious = normalizeValue(previousValue);

    // 只有当前值存在且与前值不同时，才认为是变更
    return normalizedCurrent !== null && normalizedCurrent !== normalizedPrevious;
};

// 判断操作记录是否有实际变更
const hasActualChanges = (operlogItem: any, operateContent: string) => {
    const { requestMethod } = operlogItem;

    // 创建和删除操作总是显示
    if (requestMethod === 'POST' || requestMethod === 'DELETE') {
        return true;
    }

    // 修改操作：只有在包含"修改内容："时才显示，否则说明没有实际字段变更
    if (requestMethod === 'PUT') {
        return operateContent.includes('修改内容：');
    }

    // 其他操作（GET等）默认显示
    return true;
};

// 格式化日期
const formatDate = (dateStr: string) => {
    try {
        const date = new Date(dateStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch {
        return dateStr;
    }
};

// 获取工单类型标签
const getTicketTypeLabel = (value: any) => {
    const option = work_order_type_list.value.find(item => item.dictValue == value);
    return option ? option.dictLabel : value;
};

// 获取工单级别标签
const getTicketLevelLabel = (value: any) => {
    const option = ticket_level_list.value.find(item => item.dictValue == value);
    return option ? option.dictLabel : value;
};

// 获取工单状态标签
const getTicketStatusLabel = (value: any) => {
    const option = ticket_status_list.value.find(item => item.dictValue == value);
    return option ? option.dictLabel : value;
};

// 获取请求方法标签样式
const getRequestMethodTag = (method: string) => {
    const methodMap: { [key: string]: string } = {
        'GET': 'info',
        'POST': 'success',
        'PUT': 'warning',
        'DELETE': 'danger'
    };
    return methodMap[method] || 'info';
};

// 格式化操作内容，添加HTML样式
const formatOperateContent = (content: string) => {
    if (!content) return '';

    // 高亮显示"从...变更为..."格式
    content = content.replace(/从"([^"]*)"变更为"([^"]*)"/g,
        '从<span style="color: #F56C6C; background-color: #FEF0F0; padding: 1px 4px; border-radius: 2px; font-weight: bold;">"$1"</span>变更为<span style="color: #67C23A; background-color: #F0F9FF; padding: 1px 4px; border-radius: 2px; font-weight: bold;">"$2"</span>');

    // 高亮显示字段名称（工单标题、工单状态等）
    content = content.replace(/^([^：]+：)/gm, '<strong style="color: #409EFF; font-size: 14px;">$1</strong>');

    // 高亮显示"修改内容"、"初始设置"、"当前设置"等关键词
    content = content.replace(/(修改内容|初始设置|当前设置)：/g, '<span style="color: #E6A23C; font-weight: bold; font-size: 14px;">$1：</span>');

    // 为变更行添加缩进和样式
    const lines = content.split('\n');
    const formattedLines = lines.map((line, index) => {
        if (index > 0 && line.includes('变更为')) {
            return `<div style="margin-left: 15px; padding: 4px 8px; background-color: #FAFAFA; border-left: 3px solid #409EFF; margin: 2px 0; border-radius: 3px; line-height: 1.5;">${line}</div>`;
        } else if (index > 0 && line.includes('：')) {
            return `<div style="margin-left: 10px; padding: 2px 0; line-height: 1.4;">${line}</div>`;
        }
        return `<div style="line-height: 1.4;">${line}</div>`;
    });

    return formattedLines.join('');
};

// 页面加载时
onMounted(() => {
    getTableData();
    getdictdata();
    getUserList();
});
</script>

<style scoped>
.system-dic-container {
    padding: 20px;
}

.history-section {
    border-top: 1px solid #e4e7ed;
    padding-top: 20px;
}

.history-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
}

.history-section .el-table {
    margin-bottom: 15px;
}

.history-section .el-table .el-table__cell {
    padding: 8px 0;
}

.history-section .el-tag {
    font-size: 12px;
}

.history-section .el-table .el-table__cell .cell {
    word-break: break-word;
}

.history-section .el-table .el-table__row:hover {
    background-color: #f5f7fa;
}

.history-section .el-table .el-table__cell .cell {
    padding: 8px 12px;
}

.history-section .change-item {
    margin: 2px 0;
    padding: 2px 6px;
    background-color: #f8f9fa;
    border-left: 3px solid #409EFF;
    border-radius: 3px;
}

.history-section .change-arrow {
    color: #409EFF;
    font-weight: bold;
    margin: 0 4px;
}

.work-log-section {
    border-top: 1px solid #e4e7ed;
    padding-top: 20px;
}

.work-log-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
}

.work-log-section .el-table {
    margin-bottom: 15px;
}

.work-log-section .el-table .el-table__cell {
    padding: 8px 0;
}

.work-log-section .el-table .el-table__row:hover {
    background-color: #f5f7fa;
}

.tabs-section {
    border-top: 1px solid #e4e7ed;
    padding-top: 20px;
}

.tabs-section .el-tabs--border-card {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
}

.tabs-section .el-tabs__content {
    padding: 15px;
}

.tabs-section .el-table {
    margin-bottom: 15px;
}

.tabs-section .el-table .el-table__cell {
    padding: 8px 0;
}

.tabs-section .el-table .el-table__row:hover {
    background-color: #f5f7fa;
}

.total-hours-summary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.total-hours-summary:hover {
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
    transform: translateY(-1px);
}
</style>