<template>
  <div class="scroll-board" :style="boardStyle">
    <div class="scroll-board-header" :style="headerStyle" v-if="config.header && config.header.length">
      <div 
        v-for="(headerItem, index) in config.header" 
        :key="index"
        class="scroll-board-header-item"
        :style="getHeaderItemStyle(index)"
      >
        {{ headerItem }}
      </div>
    </div>
    <div class="scroll-board-body" :style="bodyStyle">
      <div 
        class="scroll-board-row"
        v-for="(row, rowIndex) in displayData" 
        :key="rowIndex"
        :style="getRowStyle(rowIndex)"
      >
        <div 
          v-for="(cell, cellIndex) in row" 
          :key="cellIndex"
          class="scroll-board-cell"
          :style="getCellStyle(cellIndex)"
          v-html="cell"
        >
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch } from 'vue'

interface ScrollBoardConfig {
  header?: string[]
  data?: any[][]
  rowNum?: number
  headerBGC?: string
  oddRowBGC?: string
  evenRowBGC?: string
  waitTime?: number
  headerHeight?: number
  columnWidth?: string[]
  align?: string[]
  index?: boolean
  indexHeader?: string
  carousel?: string
}

interface Props {
  config: ScrollBoardConfig
}

const props = defineProps<Props>()

// 响应式数据
const displayData = ref<any[][]>([])
const currentIndex = ref(0)
let timer: NodeJS.Timeout | null = null

// 默认配置
const defaultConfig = {
  header: [],
  data: [],
  rowNum: 5,
  headerBGC: '#00BAFF',
  oddRowBGC: '#003B51',
  evenRowBGC: '#0A2732',
  waitTime: 2000,
  headerHeight: 35,
  columnWidth: [],
  align: [],
  index: false,
  indexHeader: '#',
  carousel: 'single'
}

// 合并配置
const mergedConfig = computed(() => ({
  ...defaultConfig,
  ...props.config
}))

// 样式计算
const boardStyle = computed(() => ({
  width: '100%',
  height: '100%',
  color: '#fff'
}))

const headerStyle = computed(() => ({
  display: 'flex',
  backgroundColor: mergedConfig.value.headerBGC,
  height: `${mergedConfig.value.headerHeight}px`,
  lineHeight: `${mergedConfig.value.headerHeight}px`,
  fontSize: '13px',
  fontWeight: 'bold',
  width: '100%',
  boxSizing: 'border-box'
}))

const bodyStyle = computed(() => ({
  height: `calc(100% - ${mergedConfig.value.headerHeight}px)`,
  overflow: 'hidden'
}))

// 获取表头项样式
const getHeaderItemStyle = (index: number) => {
  const width = getColumnWidth(index)
  const align = mergedConfig.value.align[index] || 'center'
  return {
    width,
    minWidth: '0',
    flexShrink: 0,
    textAlign: align,
    padding: '0 5px',
    borderRight: index < mergedConfig.value.header!.length - 1 ? '1px solid rgba(255,255,255,0.1)' : 'none',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    boxSizing: 'border-box'
  }
}

// 获取行样式
const getRowStyle = (rowIndex: number) => {
  const isOdd = rowIndex % 2 === 1
  return {
    display: 'flex',
    backgroundColor: isOdd ? mergedConfig.value.oddRowBGC : mergedConfig.value.evenRowBGC,
    height: '30px',
    lineHeight: '30px',
    fontSize: '12px',
    width: '100%',
    boxSizing: 'border-box'
  }
}

// 获取单元格样式
const getCellStyle = (cellIndex: number) => {
  const width = getColumnWidth(cellIndex)
  const align = mergedConfig.value.align[cellIndex] || 'center'
  return {
    width,
    minWidth: '0',
    flexShrink: 0,
    textAlign: align,
    padding: '0 5px',
    borderRight: cellIndex < (mergedConfig.value.header?.length || 0) - 1 ? '1px solid rgba(255,255,255,0.1)' : 'none',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    boxSizing: 'border-box'
  }
}

// 获取列宽
const getColumnWidth = (index: number) => {
  if (mergedConfig.value.columnWidth && mergedConfig.value.columnWidth.length > 0) {
    // 如果配置了具体的列宽，计算百分比
    const columnWidths = mergedConfig.value.columnWidth
    const totalWidth = columnWidths.reduce((sum, width) => sum + parseInt(width.toString()), 0)
    const currentWidth = parseInt(columnWidths[index]?.toString() || '100')
    return `${(currentWidth / totalWidth) * 100}%`
  }
  const headerLength = mergedConfig.value.header?.length || 1
  return `${100 / headerLength}%`
}

// 更新显示数据
const updateDisplayData = () => {
  const { data, rowNum, carousel } = mergedConfig.value
  if (!data || data.length === 0) {
    displayData.value = []
    return
  }

  if (data.length <= rowNum) {
    displayData.value = [...data]
    return
  }

  if (carousel === 'single') {
    // 单行滚动
    displayData.value = data.slice(currentIndex.value, currentIndex.value + rowNum)
    if (displayData.value.length < rowNum) {
      displayData.value = displayData.value.concat(data.slice(0, rowNum - displayData.value.length))
    }
  } else {
    // 整页滚动
    const startIndex = currentIndex.value * rowNum
    displayData.value = data.slice(startIndex, startIndex + rowNum)
    if (displayData.value.length === 0) {
      currentIndex.value = 0
      displayData.value = data.slice(0, rowNum)
    }
  }
}

// 开始轮播
const startCarousel = () => {
  const { data, rowNum, waitTime, carousel } = mergedConfig.value
  if (!data || data.length <= rowNum) return

  timer = setInterval(() => {
    if (carousel === 'single') {
      currentIndex.value = (currentIndex.value + 1) % data.length
    } else {
      const totalPages = Math.ceil(data.length / rowNum)
      currentIndex.value = (currentIndex.value + 1) % totalPages
    }
    updateDisplayData()
  }, waitTime)
}

// 停止轮播
const stopCarousel = () => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
}

// 监听配置变化
watch(() => props.config, () => {
  currentIndex.value = 0
  updateDisplayData()
  stopCarousel()
  startCarousel()
}, { deep: true })

// 生命周期
onMounted(() => {
  updateDisplayData()
  startCarousel()
})

onBeforeUnmount(() => {
  stopCarousel()
})
</script>

<style lang="scss" scoped>
.scroll-board {
  position: relative;
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  .scroll-board-header {
    width: 100%;
    box-sizing: border-box;

    .scroll-board-header-item {
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
    }
  }

  .scroll-board-body {
    width: 100%;
    box-sizing: border-box;

    .scroll-board-row {
      width: 100%;
      box-sizing: border-box;

      .scroll-board-cell {
        display: flex;
        align-items: center;
        box-sizing: border-box;
      }
    }
  }
}
</style>
