<template>
    <div class="image-preview">
        <el-image
            :src="src"
            :style="{ width: width + 'px', height: height + 'px' }"
            :preview-src-list="[src]"
            :initial-index="0"
            fit="cover"
            preview-teleported
            @error="handleError"
        >
            <template #error>
                <div class="image-slot">
                    <el-icon><Picture /></el-icon>
                </div>
            </template>
        </el-image>
    </div>
</template>

<script setup lang="ts">
import { Picture } from '@element-plus/icons-vue';

// 定义组件名称
defineOptions({
    name: 'ImagePreview'
});

// 定义 props
interface Props {
    src: string;
    width?: number;
    height?: number;
}

const props = withDefaults(defineProps<Props>(), {
    width: 100,
    height: 100
});

// 错误处理
const handleError = () => {
  // eslint-disable-next-line no-console
    console.warn('图片加载失败:', props.src);
};
</script>

<style lang="scss" scoped>
.image-preview {
    display: inline-block;
    
    .image-slot {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 100%;
        background: var(--el-fill-color-light);
        color: var(--el-text-color-secondary);
        font-size: 30px;
    }
}
</style>
