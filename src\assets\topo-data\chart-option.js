// 图表选项配置文件 - Vue 3 版本
const chartOption = {
  /**
   * 解析函数字符串
   * @param {string} optionStr 选项字符串
   * @returns {string} 函数字符串
   */
  getFun(optionStr) {
    try {
      if (!optionStr || optionStr.trim() === '') {
        optionStr = this.getOption();
      }

      // 处理代码字符串，确保兼容性
      let processedStr = optionStr.trim();

      // 如果代码中包含变量声明但没有 option 声明，需要特殊处理
      if (!processedStr.includes('option =') && !processedStr.includes('option=')) {
        // 检查是否是直接的对象字面量
        if (processedStr.startsWith('{') && processedStr.endsWith('}')) {
          processedStr = `var option = ${processedStr}`;
        } else {
          // 如果包含其他变量声明，将 let/const 替换为 var 以避免严格模式问题
          processedStr = processedStr.replace(/\b(let|const)\s+/g, 'var ');

          // 确保最后有 option 变量
          if (!processedStr.includes('option')) {
            processedStr += '\nvar option = typeof option !== "undefined" ? option : {};';
          }
        }
      } else {
        // 替换 let/const 为 var 以避免严格模式问题
        processedStr = processedStr.replace(/\b(let|const)\s+option\s*=/g, 'var option =');
        processedStr = processedStr.replace(/\b(let|const)\s+/g, 'var ');
      }

      const funStr = `function (echarts, echartData) {
    try {
        // 确保 echarts 对象可用
        if (!echarts) {
            console.warn('echarts 对象未定义，使用基础配置');
        }

        // 执行用户代码
        ${processedStr}

        // 确保返回有效的选项对象
        if (!option || typeof option !== 'object') {
            console.warn('option 未定义或无效，使用默认配置');
            option = {
                title: { text: '数据错误' },
                xAxis: { type: 'category', data: [] },
                yAxis: { type: 'value' },
                series: [{ name: '默认', type: 'bar', data: [] }]
            };
        }

        // 设置全局颜色调色板
        if (!option.color) {
            option.color = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de'];
        }

        // 验证和修复 series 数组
        if (option.series && Array.isArray(option.series)) {
            const defaultColors = option.color;
            option.series = option.series.map((series, index) => {
                if (!series || typeof series !== 'object') {
                    return {
                        name: '系列' + (index + 1),
                        type: 'bar',
                        data: [],
                        itemStyle: {
                            color: defaultColors[index % defaultColors.length],
                            borderColor: defaultColors[index % defaultColors.length],
                            borderWidth: 0
                        },
                        lineStyle: {
                            color: defaultColors[index % defaultColors.length]
                        }
                    };
                }

                const color = series.itemStyle?.color || defaultColors[index % defaultColors.length];

                // 确保每个 series 都有完整的颜色配置
                const enhancedSeries = {
                    name: series.name || '系列' + (index + 1),
                    type: series.type || 'bar',
                    data: Array.isArray(series.data) ? series.data : [],
                    itemStyle: {
                        color: color,
                        borderColor: color,
                        borderWidth: 0,
                        ...series.itemStyle
                    },
                    lineStyle: {
                        color: color,
                        ...series.lineStyle
                    },
                    ...series
                };

                // 为面积图添加 areaStyle
                if (series.type === 'line' && series.areaStyle) {
                    enhancedSeries.areaStyle = {
                        color: color,
                        opacity: 0.3,
                        ...series.areaStyle
                    };
                }

                return enhancedSeries;
            });
        } else if (!option.series) {
            option.series = [{
                name: '默认',
                type: 'bar',
                data: [],
                itemStyle: {
                    color: '#5470c6',
                    borderColor: '#5470c6',
                    borderWidth: 0
                },
                lineStyle: {
                    color: '#5470c6'
                }
            }];
        }

        return option;
    } catch (error) {
        console.error('图表选项执行错误:', error);
        console.error('错误的代码:', ${JSON.stringify(processedStr)});
        return {
            title: { text: '配置错误: ' + error.message },
            xAxis: { type: 'category', data: ['错误'] },
            yAxis: { type: 'value' },
            series: [{ name: '错误', type: 'bar', data: [0] }]
        };
    }
}`;
      return funStr;
    } catch (error) {
      console.error('解析图表选项失败:', error);
      return `function (echarts, echartData) {
        return {
            title: { text: '解析错误' },
            xAxis: { type: 'category', data: ['错误'] },
            yAxis: { type: 'value' },
            series: [{ name: '错误', type: 'bar', data: [0] }]
        };
      }`;
    }
  },

  /**
   * 获取地图图表选项字符串
   * @returns {string} 地图选项字符串
   */
  getOptionMap() {
    const optionStr = `let option = {
    tooltip: {
        trigger: "item",
        formatter: function(params) {
            return params.name + ': ' + (params.value || 0);
        }
    },
    visualMap: {
        min: 30,
        max: 700,
        splitNumber: 0,
        text: ["高", "低"],
        realtime: false,
        calculable: false,
        selectedMode: false,
        itemWidth: 10,
        itemHeight: 60,
        inRange: {
            color: ["lightskyblue", "yellow", "orangered"]
        }
    },
    series: [{
        name: "地图数据",
        type: "map",
        map: "mapJson",
        scaleLimit: {
            min: 0.8,
            max: 1.9
        },
        mapLocation: {
            y: 60
        },
        emphasis: {
            label: {
                show: true
            },
            itemStyle: {
                areaColor: '#389BB7',
                borderWidth: 0
            }
        },
        label: {
            show: true,
            color: '#000'
        },
        data: [
            { name: "郑州市", value: "585" },
            { name: "洛阳市", value: "450" },
            { name: "许昌市", value: "256" },
            { name: "开封市", value: "398" },
            { name: "平顶山市", value: "444" },
            { name: "安阳市", value: "74" },
            { name: "鹤壁市", value: "127" },
            { name: "新乡市", value: "269" },
            { name: "焦作市", value: "36" },
            { name: "濮阳市", value: "187" },
            { name: "漯河市", value: "33" },
            { name: "三门峡市", value: "98" },
            { name: "商丘市", value: "254" },
            { name: "周口市", value: "87" },
            { name: "驻马店市", value: "76" },
            { name: "南阳市", value: "325" },
            { name: "信阳市", value: "333" },
            { name: "济源市", value: "15" }
        ]
    }]
};`;
    return optionStr;
  },

  /**
   * 获取简单柱状图选项字符串
   * @returns {string} 柱状图选项字符串
   */
  getOption() {
    const optionStr = `let option = {
   xAxis: {
      type: 'category',
      data: ['A', 'B', 'C', 'D', 'E']
   },
   yAxis: {
      type: 'value'
   },
   series: [{
      name: '数据',
      type: 'bar',
      data: [10, 20, 30, 40, 50]
   }]
};`;
    return optionStr;
  }
};

export default chartOption;
